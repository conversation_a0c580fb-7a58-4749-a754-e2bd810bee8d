# 部署指南

本文档详细说明了如何部署 Vue3 Element Plus Admin 项目到生产环境。

## 📋 部署前检查清单

### 环境要求
- [ ] Node.js >= 16.0.0
- [ ] npm >= 7.0.0
- [ ] Nginx >= 1.18.0 (推荐)
- [ ] 支持 Gzip/Brotli 压缩的 Web 服务器

### 配置检查
- [ ] 更新 `.env.production` 中的 API 地址
- [ ] 确认 CDN 配置是否启用
- [ ] 检查压缩配置是否开启
- [ ] 验证路由配置正确性

## 🚀 构建部署

### 1. 本地构建

```bash
# 安装依赖
npm install

# 生产构建
npm run build

# 构建分析 (可选)
npm run build:analyze
```

### 2. 构建产物说明

构建完成后，`dist` 目录包含：

```
dist/
├── index.html              # 主页面
├── index.html.gz           # Gzip 压缩版本
├── index.html.br           # Brotli 压缩版本
├── favicon.ico             # 网站图标
└── assets/
    ├── css/                # 样式文件
    │   ├── *.css           # 原始 CSS
    │   ├── *.css.gz        # Gzip 压缩 CSS
    │   └── *.css.br        # Brotli 压缩 CSS
    └── js/                 # JavaScript 文件
        ├── *.js            # 原始 JS
        ├── *.js.gz         # Gzip 压缩 JS
        └── *.js.br         # Brotli 压缩 JS
```

### 3. 性能优化效果

- **Gzip 压缩率**: 平均 70-80%
- **Brotli 压缩率**: 平均 75-85%
- **代码分割**: 自动分割为多个 chunk
- **CDN 外部化**: 主要依赖库使用 CDN

## 🐳 Docker 部署

### 1. 使用 Dockerfile

```bash
# 构建镜像
docker build -t vue-admin .

# 运行容器
docker run -d -p 80:80 vue-admin
```

### 2. 使用 Docker Compose

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 🌐 Nginx 部署

### 1. 基础配置

将 `dist` 目录内容复制到 Nginx 根目录：

```bash
# 复制文件
cp -r dist/* /var/www/html/

# 设置权限
chown -R nginx:nginx /var/www/html/
chmod -R 755 /var/www/html/
```

### 2. Nginx 配置

使用项目根目录的 `nginx.conf` 配置文件：

```bash
# 复制配置
cp nginx.conf /etc/nginx/sites-available/vue-admin
ln -s /etc/nginx/sites-available/vue-admin /etc/nginx/sites-enabled/

# 测试配置
nginx -t

# 重载配置
nginx -s reload
```

### 3. 启用压缩

确保 Nginx 配置中启用了压缩：

```nginx
# Gzip 压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_comp_level 6;

# Brotli 压缩 (需要安装模块)
brotli on;
brotli_comp_level 6;
```

## ☁️ CDN 部署

### 1. 静态资源 CDN

将 `dist/assets` 目录上传到 CDN：

```bash
# 示例：上传到阿里云 OSS
ossutil cp -r dist/assets oss://your-bucket/assets/
```

### 2. 更新资源路径

在 `vite.config.js` 中配置 CDN 路径：

```javascript
export default defineConfig({
  base: 'https://cdn.yourdomain.com/',
  // ...其他配置
})
```

## 🔧 环境配置

### 生产环境变量

更新 `.env.production`：

```bash
# API 基础路径
VITE_API_BASE_URL=https://api.yourdomain.com

# 应用标题
VITE_APP_TITLE=Vue3 Element Plus Admin

# 启用 CDN
VITE_USE_CDN=true

# 启用压缩
VITE_USE_COMPRESSION=true
```

### API 代理配置

在生产环境中，通常需要配置反向代理：

```nginx
location /api/ {
    proxy_pass http://backend-server:8080/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 📊 性能监控

### 1. 构建分析

```bash
# 生成构建报告
npm run build:report

# 分析构建结果
npm run build:analyze
```

### 2. 性能指标

监控以下关键指标：

- **首屏加载时间** < 3s
- **资源加载时间** < 2s
- **压缩率** > 70%
- **缓存命中率** > 90%

## 🔒 安全配置

### 1. HTTPS 配置

```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
}
```

### 2. 内容安全策略

```nginx
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://unpkg.com; style-src 'self' 'unsafe-inline' https://unpkg.com;" always;
```

## 🚨 故障排除

### 常见问题

1. **路由 404 错误**
   - 检查 Nginx 配置中的 `try_files` 指令
   - 确保启用了 Vue Router 历史模式支持

2. **静态资源加载失败**
   - 检查资源路径配置
   - 验证 CDN 配置是否正确

3. **API 请求失败**
   - 检查代理配置
   - 验证 CORS 设置

4. **压缩文件不生效**
   - 检查 Nginx 压缩配置
   - 验证浏览器支持情况

### 日志查看

```bash
# Nginx 访问日志
tail -f /var/log/nginx/access.log

# Nginx 错误日志
tail -f /var/log/nginx/error.log

# Docker 容器日志
docker logs -f container-name
```

## 📞 技术支持

如遇到部署问题，请：

1. 检查本文档的故障排除部分
2. 查看项目 Issues
3. 联系技术支持团队

---

🎉 部署完成后，访问您的域名即可使用系统！
