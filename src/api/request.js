/**
 * 统一的API请求管理
 * 基于axios的请求封装，支持拦截器、错误处理等
 */

import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentUser } from '@/utils/permission'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // API基础URL
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加用户信息到请求头
    const userInfo = getCurrentUser()
    if (userInfo) {
      config.headers['X-User-ID'] = userInfo.id
      config.headers['X-User-Role'] = userInfo.role
    }
    
    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    // 打印请求信息（开发环境）
    if (import.meta.env.DEV) {
      console.log('🚀 API Request:', {
        url: config.url,
        method: config.method,
        params: config.params,
        data: config.data
      })
    }
    
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    
    // 打印响应信息（开发环境）
    if (import.meta.env.DEV) {
      console.log('✅ API Response:', {
        url: response.config.url,
        status: response.status,
        data: response.data
      })
    }
    
    const { data, status } = response

    // 根据业务状态码处理响应
    if (data && typeof data === 'object') {
      // 标准响应格式: { code, data, message }
      if (data.code !== undefined) {
        switch (data.code) {
          case 200:
          case 0:
            // 成功
            return data
            
          case 401:
            // 未授权，清除token并跳转登录
            localStorage.removeItem('token')
            localStorage.removeItem('userInfo')
            ElMessage.error('登录已过期，请重新登录')
            window.location.href = '/login'
            return Promise.reject(new Error('未授权'))
            
          case 403:
            // 权限不足
            ElMessage.error('权限不足，无法执行此操作')
            return Promise.reject(new Error('权限不足'))
            
          case 404:
            // 资源不存在
            ElMessage.error('请求的资源不存在')
            return Promise.reject(new Error('资源不存在'))
            
          case 500:
            // 服务器错误
            ElMessage.error('服务器内部错误，请稍后重试')
            return Promise.reject(new Error('服务器错误'))
            
          default:
            // 其他错误
            ElMessage.error(data.msg || '请求失败')
            return Promise.reject(new Error(data.msg || '请求失败'))
        }
      }
    }
    
    // 直接返回响应数据
    return response
  },
  error => {
    // 对响应错误做点什么
    
    console.error('❌ Response Error:', error)
    
    let message = '网络错误，请检查网络连接'
    
    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data?.msg || '请求参数错误'
          break
        case 401:
          message = '登录已过期，请重新登录'
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          setTimeout(() => {
            window.location.href = '/login'
          }, 1000)
          break
        case 403:
          message = '权限不足，无法访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 422:
          message = data?.msg || '数据验证失败'
          break
        case 429:
          message = '请求过于频繁，请稍后重试'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务暂时不可用'
          break
        case 504:
          message = '网关超时'
          break
        default:
          message = data?.msg || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      message = '网络连接超时，请检查网络'
    } else {
      // 其他错误
      message = error.message || '请求配置错误'
    }
    
    // 显示错误消息
    ElMessage.error(message)
    
    return Promise.reject(error)
  }
)

// 通用请求方法
export const request = {
  // GET请求
  get(url, params = {}, config = {}) {
    return service({
      method: 'get',
      url,
      params,
      ...config
    })
  },
  
  // POST请求
  post(url, data = {}, config = {}) {
    return service({
      method: 'post',
      url,
      data,
      ...config
    })
  },
  
  // PUT请求
  put(url, data = {}, config = {}) {
    return service({
      method: 'put',
      url,
      data,
      ...config
    })
  },
  
  // PATCH请求
  patch(url, data = {}, config = {}) {
    return service({
      method: 'patch',
      url,
      data,
      ...config
    })
  },
  
  // DELETE请求
  delete(url, params = {}, config = {}) {
    return service({
      method: 'delete',
      url,
      params,
      ...config
    })
  },
  
  // 文件上传
  upload(url, formData, config = {}) {
    return service({
      method: 'post',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  },
  
  // 文件下载
  download(url, params = {}, config = {}) {
    return service({
      method: 'get',
      url,
      params,
      responseType: 'blob',
      ...config
    })
  }
}

// 导出axios实例（用于特殊情况）
export default service

// 导出http对象（兼容新的API模块）
export const http = request

// 请求工具函数

/**
 * 批量请求
 * @param {Array} requests 请求数组
 * @returns {Promise}
 */
export function batchRequest(requests) {
  return Promise.all(requests)
}

/**
 * 并发控制请求
 * @param {Array} requests 请求数组
 * @param {number} limit 并发限制
 * @returns {Promise}
 */
export async function concurrentRequest(requests, limit = 3) {
  const results = []
  const executing = []
  
  for (const request of requests) {
    const promise = request().then(result => {
      executing.splice(executing.indexOf(promise), 1)
      return result
    })
    
    results.push(promise)
    
    if (requests.length >= limit) {
      executing.push(promise)
      
      if (executing.length >= limit) {
        await Promise.race(executing)
      }
    }
  }
  
  return Promise.all(results)
}

/**
 * 重试请求
 * @param {Function} requestFn 请求函数
 * @param {number} maxRetries 最大重试次数
 * @param {number} delay 重试延迟（毫秒）
 * @returns {Promise}
 */
export async function retryRequest(requestFn, maxRetries = 3, delay = 1000) {
  let lastError
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      lastError = error
      
      if (i < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
      }
    }
  }
  
  throw lastError
}

/**
 * 请求缓存
 */
class RequestCache {
  constructor() {
    this.cache = new Map()
    this.timeouts = new Map()
  }
  
  // 生成缓存key
  generateKey(url, params) {
    return `${url}?${JSON.stringify(params)}`
  }
  
  // 设置缓存
  set(url, params, data, ttl = 300000) { // 默认5分钟
    const key = this.generateKey(url, params)
    this.cache.set(key, data)
    
    // 设置过期时间
    if (this.timeouts.has(key)) {
      clearTimeout(this.timeouts.get(key))
    }
    
    const timeout = setTimeout(() => {
      this.cache.delete(key)
      this.timeouts.delete(key)
    }, ttl)
    
    this.timeouts.set(key, timeout)
  }
  
  // 获取缓存
  get(url, params) {
    const key = this.generateKey(url, params)
    return this.cache.get(key)
  }
  
  // 删除缓存
  delete(url, params) {
    const key = this.generateKey(url, params)
    const timeout = this.timeouts.get(key)
    
    if (timeout) {
      clearTimeout(timeout)
      this.timeouts.delete(key)
    }
    
    return this.cache.delete(key)
  }
  
  // 清空缓存
  clear() {
    this.timeouts.forEach(timeout => clearTimeout(timeout))
    this.cache.clear()
    this.timeouts.clear()
  }
}

// 导出缓存实例
export const requestCache = new RequestCache()

/**
 * 带缓存的GET请求
 * @param {string} url 请求URL
 * @param {object} params 请求参数
 * @param {object} config 配置选项
 * @param {number} ttl 缓存时间（毫秒）
 * @returns {Promise}
 */
export function cachedGet(url, params = {}, config = {}, ttl = 300000) {
  // 检查缓存
  const cached = requestCache.get(url, params)
  if (cached) {
    return Promise.resolve(cached)
  }
  
  // 发起请求
  return request.get(url, params, config).then(data => {
    // 缓存结果
    requestCache.set(url, params, data, ttl)
    return data
  })
}
