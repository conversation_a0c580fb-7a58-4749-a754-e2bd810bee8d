/**
 * API统一入口文件
 * 导出所有API模块，方便统一管理和使用
 */

// 导出请求工具
export { 
  request, 
  batchRequest, 
  concurrentRequest, 
  retryRequest, 
  requestCache, 
  cachedGet 
} from './request'

// 导入API模块
import * as authModule from './modules/auth'
import * as userModule from './modules/user'
import * as dashboardModule from './modules/dashboard'
import * as orderModule from './order'
import * as configModule from './config'
import * as productModule from './product'
import * as adminUserModule from './admin-user'

// 导出认证相关API
export * as authAPI from './modules/auth'

// 导出用户管理API
export * as userAPI from './modules/user'

// 导出控制台API
export * as dashboardAPI from './modules/dashboard'

// 导出订单管理API
export * as orderAPI from './order'

// 导出配置管理API
export * as configAPI from './config'

// 导出商品管理API
export * as productAPI from './product'

// 导出后台用户管理API
export * as adminUserAPI from './admin-user'

// 创建API实例对象，方便按模块使用
export const api = {
  auth: authModule,
  user: userModule,
  dashboard: dashboardModule,
  order: orderModule,
  config: configModule,
  product: productModule,
  adminUser: adminUserModule
}

// 默认导出（方便直接导入使用）
export default {
  // 认证相关
  auth: {
    login: authModule.login,
    logout: authModule.logout,
    getUserInfo: authModule.getUserInfo,
    changePassword: authModule.changePassword,
    refreshToken: authModule.refreshToken
  },
  
  // 用户管理
  user: {
    list: userModule.getUserList,
    detail: userModule.getUserDetail,
    create: userModule.createUser,
    update: userModule.updateUser,
    delete: userModule.deleteUser,
    batchDelete: userModule.batchDeleteUsers,
    toggleStatus: userModule.toggleUserStatus,
    resetPassword: userModule.resetUserPassword,
    updateAvatar: userModule.updateUserAvatar,
    export: userModule.exportUsers,
    import: userModule.importUsers,
    checkUsername: userModule.checkUsername,
    checkEmail: userModule.checkEmail
  },
  
  // 控制台
  dashboard: {
    stats: dashboardModule.getDashboardStats,
    realtime: dashboardModule.getRealTimeData,
    charts: dashboardModule.getChartData,
    monitor: dashboardModule.getSystemMonitor,
    activities: dashboardModule.getRecentActivities,
    notices: dashboardModule.getSystemNotices,
    todos: dashboardModule.getTodoList
  },

  // 订单管理
  order: {
    list: orderModule.getOrderList,
    detail: orderModule.getOrderDetail,
    create: orderModule.createOrder,
    update: orderModule.updateOrder,
    delete: orderModule.deleteOrder,
    batchDelete: orderModule.batchDeleteOrders,
    updateStatus: orderModule.updateOrderStatus,
    batchUpdateStatus: orderModule.batchUpdateOrderStatus,
    export: orderModule.exportOrders,
    stats: orderModule.getOrderStats
  },

  // 配置管理
  config: {
    list: configModule.getConfigList,
    detail: configModule.getConfigDetail,
    create: configModule.createConfig,
    update: configModule.updateConfig,
    delete: configModule.deleteConfig,
    batchDelete: configModule.batchDeleteConfigs,
    getByName: configModule.getConfigByName,
    getAll: configModule.getAllConfigs,
    export: configModule.exportConfigs,
    import: configModule.importConfigs
  },

  // 商品管理
  product: {
    list: productModule.getProductList,
    detail: productModule.getProductDetail,
    create: productModule.createProduct,
    update: productModule.updateProduct,
    delete: productModule.deleteProduct,
    batchDelete: productModule.batchDeleteProducts,
    updateStatus: productModule.updateProductStatus,
    batchUpdateStatus: productModule.batchUpdateProductStatus,
    uploadImage: productModule.uploadProductImage,
    export: productModule.exportProducts,
    import: productModule.importProducts,
    stats: productModule.getProductStats
  },

  // 后台用户管理
  adminUser: {
    list: adminUserModule.getAdminUserList,
    detail: adminUserModule.getAdminUserDetail,
    create: adminUserModule.createAdminUser,
    update: adminUserModule.updateAdminUser,
    delete: adminUserModule.deleteAdminUser,
    batchDelete: adminUserModule.batchDeleteAdminUsers,
    resetPassword: adminUserModule.resetAdminUserPassword,
    updateStatus: adminUserModule.updateAdminUserStatus,
    updateRole: adminUserModule.updateAdminUserRole,
    forceLogout: adminUserModule.forceAdminUserLogout,
    export: adminUserModule.exportAdminUsers,
    import: adminUserModule.importAdminUsers
  }
}

/**
 * API快捷方法
 * 提供常用的API调用方法
 */

// 用户相关快捷方法
export const userQuick = {
  // 获取当前用户信息
  async getCurrentUser() {
    const { getUserInfo } = await import('./modules/auth')
    return getUserInfo()
  },
  
  // 检查用户是否存在
  async checkUserExists(username, email) {
    const { checkUsername, checkEmail } = await import('./modules/user')
    const [usernameExists, emailExists] = await Promise.all([
      checkUsername(username).catch(() => ({ exists: false })),
      checkEmail(email).catch(() => ({ exists: false }))
    ])
    return {
      username: usernameExists.exists,
      email: emailExists.exists
    }
  },
  
  // 获取用户统计
  async getUserStatistics() {
    const { getUserStats } = await import('./modules/user')
    return getUserStats()
  }
}

// 认证相关快捷方法
export const authQuick = {
  // 检查登录状态
  async checkLoginStatus() {
    const token = localStorage.getItem('token')
    const userInfo = localStorage.getItem('userInfo')

    // 如果没有 token 和用户信息，认为未登录
    if (!token && !userInfo) return false

    // 如果有 token，验证其有效性
    if (token) {
      try {
        const { validateToken } = await import('./modules/auth')
        await validateToken(token)
        return true
      } catch {
        // token 无效，清除本地数据
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        return false
      }
    }

    // 如果只有用户信息没有 token，可能是通过 cookie 认证
    // 尝试获取用户信息来验证认证状态
    if (userInfo) {
      try {
        const { getUserInfo } = await import('./modules/auth')
        await getUserInfo()
        return true
      } catch {
        // 认证失败，清除用户信息
        localStorage.removeItem('userInfo')
        return false
      }
    }

    return false
  },

  // 安全登出
  async safeLogout() {
    try {
      const { logout } = await import('./modules/auth')
      await logout()
    } catch (error) {
      console.warn('登出API调用失败:', error)
    } finally {
      // 无论API是否成功，都清除本地数据
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')

      // 尝试清除认证相关的 cookie
      document.cookie.split(';').forEach(cookie => {
        const [name] = cookie.trim().split('=')
        if (name && (name.includes('auth') || name.includes('token') || name.includes('session'))) {
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
        }
      })

      window.location.href = '/login'
    }
  }
}

// 控制台相关快捷方法
export const dashboardQuick = {
  // 获取完整的控制台数据
  async getFullDashboardData() {
    const { 
      getDashboardStats, 
      getChartData, 
      getSystemMonitor, 
      getRecentActivities 
    } = await import('./modules/dashboard')
    
    const [stats, charts, monitor, activities] = await Promise.all([
      getDashboardStats().catch(() => ({})),
      getChartData('visits', 'week').catch(() => ({})),
      getSystemMonitor().catch(() => ({})),
      getRecentActivities(5).catch(() => [])
    ])
    
    return { stats, charts, monitor, activities }
  },
  
  // 刷新实时数据
  async refreshRealTimeData() {
    const { getRealTimeData, getSystemMonitor } = await import('./modules/dashboard')
    return Promise.all([
      getRealTimeData().catch(() => ({})),
      getSystemMonitor().catch(() => ({}))
    ])
  }
}

/**
 * API错误处理工具
 */
export const apiUtils = {
  // 统一错误处理
  handleError(error, customMessage = '操作失败') {
    console.error('API Error:', error)
    
    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 401:
          return '登录已过期，请重新登录'
        case 403:
          return '权限不足，无法执行此操作'
        case 404:
          return '请求的资源不存在'
        case 422:
          return data?.message || '数据验证失败'
        case 500:
          return '服务器内部错误'
        default:
          return data?.message || customMessage
      }
    }
    
    return error.message || customMessage
  },
  
  // 重试机制
  async withRetry(apiCall, maxRetries = 3, delay = 1000) {
    let lastError
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await apiCall()
      } catch (error) {
        lastError = error
        
        if (i < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
        }
      }
    }
    
    throw lastError
  },
  
  // 并发限制
  async withConcurrencyLimit(apiCalls, limit = 3) {
    const results = []
    const executing = []
    
    for (const apiCall of apiCalls) {
      const promise = apiCall().then(result => {
        executing.splice(executing.indexOf(promise), 1)
        return result
      })
      
      results.push(promise)
      
      if (apiCalls.length >= limit) {
        executing.push(promise)
        
        if (executing.length >= limit) {
          await Promise.race(executing)
        }
      }
    }
    
    return Promise.all(results)
  }
}

/**
 * Mock数据开关
 * 在开发环境下可以切换到Mock数据
 */
export const mockConfig = {
  enabled: import.meta.env.VITE_USE_MOCK === 'true',
  
  // 启用Mock
  enable() {
    this.enabled = true
  },
  
  // 禁用Mock
  disable() {
    this.enabled = false
  },
  
  // 检查是否使用Mock
  shouldUseMock() {
    return this.enabled && import.meta.env.DEV
  }
}
