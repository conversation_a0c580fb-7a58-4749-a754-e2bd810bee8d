/**
 * 认证相关API
 */

import { request } from '../request'

/**
 * 用户登录
 * @param {Object} data 登录数据
 * @param {string} data.username 用户名
 * @param {string} data.password 密码
 * @param {string} data.role 角色
 * @returns {Promise}
 */
export function login(data) {
  return request.post('/auth/login', data)
}

/**
 * 用户登出
 * @returns {Promise}
 */
export function logout() {
  return request.post('/auth/logout')
}

/**
 * 刷新token
 * @param {string} refreshToken 刷新token
 * @returns {Promise}
 */
export function refreshToken(refreshToken) {
  return request.post('/auth/refresh', { refreshToken })
}

/**
 * 获取用户信息
 * @returns {Promise}
 */
export function getUserInfo() {
  return request.get('/auth/userinfo')
}

/**
 * 修改密码
 * @param {Object} data 密码数据
 * @param {string} data.oldPassword 旧密码
 * @param {string} data.newPassword 新密码
 * @returns {Promise}
 */
export function changePassword(data) {
  return request.put('/auth/password', data)
}

/**
 * 忘记密码
 * @param {string} email 邮箱地址
 * @returns {Promise}
 */
export function forgotPassword(email) {
  return request.post('/auth/forgot-password', { email })
}

/**
 * 重置密码
 * @param {Object} data 重置数据
 * @param {string} data.token 重置token
 * @param {string} data.password 新密码
 * @returns {Promise}
 */
export function resetPassword(data) {
  return request.post('/auth/reset-password', data)
}

/**
 * 验证token有效性
 * @param {string} token 待验证的token
 * @returns {Promise}
 */
export function validateToken(token) {
  return request.post('/auth/validate', { token })
}

/**
 * 获取登录历史
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getLoginHistory(params) {
  return request.get('/auth/login-history', params)
}

/**
 * 两步验证相关API
 */
export const twoFactor = {
  /**
   * 获取二维码
   * @returns {Promise}
   */
  getQRCode() {
    return request.get('/auth/2fa/qrcode')
  },

  /**
   * 启用两步验证
   * @param {string} code 验证码
   * @returns {Promise}
   */
  enable(code) {
    return request.post('/auth/2fa/enable', { code })
  },

  /**
   * 禁用两步验证
   * @param {string} code 验证码
   * @returns {Promise}
   */
  disable(code) {
    return request.post('/auth/2fa/disable', { code })
  },

  /**
   * 验证两步验证码
   * @param {string} code 验证码
   * @returns {Promise}
   */
  verify(code) {
    return request.post('/auth/2fa/verify', { code })
  }
}
