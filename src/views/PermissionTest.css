.permission-test-page {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.permissions-grid {
  min-height: 60px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 6px;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.test-buttons .el-button {
  margin: 0;
}

:deep(.el-descriptions__body .el-descriptions__table) {
  width: 100%;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}
