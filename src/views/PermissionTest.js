import { ref, computed, onMounted } from 'vue'
import { 
  getCurrentUser, 
  getCurrentUserPermissions, 
  getRoleConfig,
  getPermissionMessage,
  hasPermission,
  handlePermissionDenied,
  usePermissionCheck
} from '@/utils/permission'

/**
 * 权限测试页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function usePermissionTest() {
  // 当前用户信息
  const currentUser = ref(null)

  // 用户权限列表
  const userPermissions = computed(() => getCurrentUserPermissions())

  // 角色配置
  const roleConfig = computed(() => {
    const user = currentUser.value
    return user ? getRoleConfig(user.role) : null
  })

  // 权限检查组合式函数
  const { checkPermission, checkAnyPermission } = usePermissionCheck()

  // 获取权限名称
  const getPermissionName = (permission) => {
    return getPermissionMessage(permission)
  }

  // 测试单个权限
  const testPermission = (permission) => {
    console.log(`测试权限: ${permission}`)
    
    if (hasPermission(permission)) {
      console.log('权限检查通过')
      // 这里可以执行具体的业务逻辑
      alert(`权限检查通过！您有 "${getPermissionName(permission)}" 权限。`)
    } else {
      console.log('权限检查失败，触发权限拒绝处理')
      handlePermissionDenied(permission)
    }
  }

  // 测试多个权限（任一）
  const testAnyPermissions = (permissions) => {
    console.log(`测试权限（任一）: ${permissions.join(', ')}`)
    
    const hasAny = permissions.some(permission => hasPermission(permission))
    
    if (hasAny) {
      console.log('权限检查通过')
      alert(`权限检查通过！您拥有其中一个权限。`)
    } else {
      console.log('权限检查失败，触发权限拒绝处理')
      const permissionNames = permissions.map(p => getPermissionName(p)).join('、')
      handlePermissionDenied(permissions[0], {
        message: `您没有执行此操作的权限。\n需要权限（任一）：${permissionNames}`
      })
    }
  }

  // 测试组合式函数权限检查
  const testComposablePermission = (permission) => {
    console.log(`使用组合式函数测试权限: ${permission}`)
    
    const result = checkPermission(permission, true)
    if (result) {
      alert(`权限检查通过！您有 "${getPermissionName(permission)}" 权限。`)
    }
    // 权限检查失败时，组合式函数会自动弹窗
  }

  // 测试组合式函数多权限检查
  const testComposableAnyPermissions = (permissions) => {
    console.log(`使用组合式函数测试权限（任一）: ${permissions.join(', ')}`)
    
    const result = checkAnyPermission(permissions, true)
    if (result) {
      alert(`权限检查通过！您拥有其中一个权限。`)
    }
    // 权限检查失败时，组合式函数会自动弹窗
  }

  // 页面初始化
  onMounted(() => {
    currentUser.value = getCurrentUser()
    console.log('当前用户:', currentUser.value)
    console.log('用户权限:', userPermissions.value)
  })

  // 返回模板需要的所有数据和方法
  return {
    currentUser,
    userPermissions,
    roleConfig,
    getPermissionName,
    testPermission,
    testAnyPermissions,
    testComposablePermission,
    testComposableAnyPermissions
  }
}
