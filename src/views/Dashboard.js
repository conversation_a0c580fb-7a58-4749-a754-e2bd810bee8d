import { ref, reactive, computed, onMounted } from 'vue'
import { 
  User, 
  ShoppingCart, 
  Money, 
  View, 
  CaretTop, 
  CaretBottom 
} from '@element-plus/icons-vue'
import { getUserInfo } from '@/router/index.js'

/**
 * 仪表板页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useDashboard() {
  // 用户信息
  const userInfo = ref({})

  // 当前日期
  const currentDate = computed(() => {
    return new Date().toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  })

  // 统计数据
  const todayVisits = ref(1856)
  const totalUsers = ref(1234)
  const systemStatus = ref('正常')

  // 图表数据
  const activeTab = ref('day')
  const chartData = ref([
    { label: '周一', value: 85 },
    { label: '周二', value: 92 },
    { label: '周三', value: 78 },
    { label: '周四', value: 96 },
    { label: '周五', value: 88 },
    { label: '周六', value: 65 },
    { label: '周日', value: 72 }
  ])

  // 饼图数据
  const pieData = ref([
    { name: '管理员', value: 15, color: '#409eff' },
    { name: '编辑员', value: 25, color: '#67c23a' },
    { name: '普通用户', value: 60, color: '#e6a23c' }
  ])

  // 活动数据
  const activities = ref([
    {
      id: 1,
      name: '张三',
      action: '创建了新用户',
      time: '5分钟前',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    {
      id: 2,
      name: '李四',
      action: '更新了系统配置',
      time: '10分钟前',
      avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'
    },
    {
      id: 3,
      name: '王五',
      action: '删除了过期数据',
      time: '15分钟前',
      avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
    },
    {
      id: 4,
      name: '赵六',
      action: '导出了用户报表',
      time: '20分钟前',
      avatar: 'https://cube.elemecdn.com/6/94/4d3ea53c9c551ba2f93c97b0deb3a5png.png'
    }
  ])

  // 切换图表时间段
  const setActiveTab = (tab) => {
    activeTab.value = tab
    // 这里可以根据tab更新chartData
  }

  // 初始化数据
  const initData = () => {
    const user = getUserInfo()
    if (user) {
      userInfo.value = user
    }
  }

  // 页面初始化
  onMounted(() => {
    initData()
  })

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    User,
    ShoppingCart,
    Money,
    View,
    CaretTop,
    CaretBottom,
    // 响应式数据
    userInfo,
    currentDate,
    todayVisits,
    totalUsers,
    systemStatus,
    activeTab,
    chartData,
    pieData,
    activities,
    // 方法
    setActiveTab
  }
}
