import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'

/**
 * 用户列表页面组合式函数（简化配置版本）
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useUserListV2() {
  const router = useRouter()

  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(5)
  const total = ref(0)
  const loading = ref(false)

  // 搜索表单配置 - 使用超简化配置
  const searchFormConfig = {
    labelWidth: '80px',
    fields: [
      'username',
      'email', 
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        options: [
          { label: '启用', value: '1' },
          { label: '禁用', value: '0' }
        ]
      }
    ],
    actions: [
      { type: 'submit', label: '搜索', style: 'primary' },
      { type: 'reset', label: '重置', style: 'default' }
    ]
  }

  // 搜索表单数据
  const searchFormData = reactive({
    username: '',
    email: '',
    status: ''
  })

  // 表格配置 - 使用超简化配置
  const tableConfig = {
    columns: [
      'id',
      {
        prop: 'avatar',
        label: '头像',
        type: 'image',
        width: 80,
        imageStyle: { width: '40px', height: '40px', borderRadius: '50%' }
      },
      'username',
      { prop: 'realName', label: '真实姓名' },
      'email',
      { prop: 'phone', label: '手机号' },
      {
        prop: 'role',
        label: '角色',
        type: 'tag',
        tagMap: {
          'admin': { type: 'danger', text: '管理员' },
          'user': { type: 'primary', text: '普通用户' },
          'editor': { type: 'warning', text: '编辑员' }
        }
      },
      'status',
      'createTime',
      { prop: 'lastLoginTime', label: '最后登录', type: 'datetime' }
    ],
    actions: [
      { type: 'view', label: '查看', style: 'text' },
      { type: 'edit', label: '编辑', style: 'primary' },
      { type: 'delete', label: '删除', style: 'danger' }
    ],
    pagination: true,
    selection: true
  }

  // 表格数据
  const tableData = ref([])

  // 模拟用户数据
  const mockUsers = [
    {
      id: 1,
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      username: 'admin',
      realName: '管理员',
      email: '<EMAIL>',
      phone: '13800138001',
      role: 'admin',
      status: '1',
      createTime: '2024-01-01 10:00:00',
      lastLoginTime: '2024-06-19 10:30:00'
    },
    {
      id: 2,
      avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
      username: 'editor01',
      realName: '张编辑',
      email: '<EMAIL>',
      phone: '13800138002',
      role: 'editor',
      status: '1',
      createTime: '2024-01-15 14:20:00',
      lastLoginTime: '2024-06-18 16:45:00'
    },
    {
      id: 3,
      avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      username: 'user001',
      realName: '李用户',
      email: '<EMAIL>',
      phone: '13800138003',
      role: 'user',
      status: '1',
      createTime: '2024-02-01 09:15:00',
      lastLoginTime: '2024-06-17 11:20:00'
    },
    {
      id: 4,
      avatar: 'https://cube.elemecdn.com/6/94/4d3ea53c9c551ba2f93c97b0deb3a5png.png',
      username: 'testuser',
      realName: '王测试',
      email: '<EMAIL>',
      phone: '13800138004',
      role: 'user',
      status: '0',
      createTime: '2024-03-10 16:30:00',
      lastLoginTime: '2024-05-20 10:15:00'
    },
    {
      id: 5,
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      username: 'designer',
      realName: '赵设计',
      email: '<EMAIL>',
      phone: '13800138005',
      role: 'editor',
      status: '1',
      createTime: '2024-04-05 11:45:00',
      lastLoginTime: '2024-06-19 09:30:00'
    },
    {
      id: 6,
      avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      username: 'manager',
      realName: '刘经理',
      email: '<EMAIL>',
      phone: '13800138006',
      role: 'admin',
      status: '1',
      createTime: '2024-02-20 13:00:00',
      lastLoginTime: '2024-06-19 08:15:00'
    },
    {
      id: 7,
      avatar: 'https://cube.elemecdn.com/6/94/4d3ea53c9c551ba2f93c97b0deb3a5png.png',
      username: 'visitor',
      realName: '访客用户',
      email: '<EMAIL>',
      phone: '13800138007',
      role: 'user',
      status: '0',
      createTime: '2024-05-01 16:00:00',
      lastLoginTime: '2024-05-15 14:30:00'
    },
    {
      id: 8,
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      username: 'developer',
      realName: '程序员',
      email: '<EMAIL>',
      phone: '13800138008',
      role: 'editor',
      status: '1',
      createTime: '2024-03-25 10:30:00',
      lastLoginTime: '2024-06-18 20:45:00'
    }
  ]

  // 获取用户列表
  const fetchUserList = async () => {
    loading.value = true
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // 模拟搜索过滤
      let filteredData = [...mockUsers]
      
      if (searchFormData.username) {
        filteredData = filteredData.filter(user => 
          user.username.includes(searchFormData.username) || 
          user.realName.includes(searchFormData.username)
        )
      }
      
      if (searchFormData.email) {
        filteredData = filteredData.filter(user => 
          user.email.includes(searchFormData.email)
        )
      }
      
      if (searchFormData.status) {
        filteredData = filteredData.filter(user => 
          user.status === searchFormData.status
        )
      }
      
      // 模拟分页
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      
      tableData.value = filteredData.slice(start, end)
      total.value = filteredData.length
      
    } catch (error) {
      ElMessage.error('获取用户列表失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  // 搜索用户
  const handleSearch = (formData) => {
    Object.assign(searchFormData, formData)
    currentPage.value = 1
    fetchUserList()
    ElMessage.success('搜索完成')
  }

  // 重置搜索
  const handleReset = () => {
    Object.assign(searchFormData, {
      username: '',
      email: '',
      status: ''
    })
    currentPage.value = 1
    fetchUserList()
    ElMessage.info('已重置搜索条件')
  }

  // 新增用户
  const handleAdd = () => {
    router.push('/admin/user-create-v2')
  }

  // 表格操作处理
  const handleTableAction = ({ type, row }) => {
    switch (type) {
      case 'view':
        ElMessage.info(`查看用户：${row.realName}`)
        // router.push(`/admin/user-edit/${row.id}?mode=view`)
        break
      case 'edit':
        ElMessage.success(`编辑用户：${row.realName}`)
        // router.push(`/admin/user-edit/${row.id}`)
        break
      case 'delete':
        handleDelete(row)
        break
      default:
        ElMessage.warning(`未知操作：${type}`)
    }
  }

  // 删除用户
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除用户 "${row.realName}" 吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      // 模拟删除API
      await new Promise(resolve => setTimeout(resolve, 500))
      
      ElMessage.success(`用户 "${row.realName}" 删除成功`)
      fetchUserList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 选择变化
  const handleSelectionChange = (selection) => {
    console.log('选中的用户：', selection)
    if (selection.length > 0) {
      ElMessage.info(`已选中 ${selection.length} 个用户`)
    }
  }

  // 分页改变
  const handlePageChange = (page) => {
    currentPage.value = page
    fetchUserList()
  }

  // 页面大小改变
  const handleSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1
    fetchUserList()
  }

  // 页面加载时获取数据
  onMounted(() => {
    fetchUserList()
  })

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    Plus,
    Search,
    Refresh,
    // 响应式数据
    searchFormConfig,
    searchFormData,
    tableData,
    tableConfig,
    loading,
    total,
    currentPage,
    pageSize,
    // 方法
    handleAdd,
    handleSearch,
    handleReset,
    handleTableAction,
    handleSelectionChange,
    handlePageChange,
    handleSizeChange
  }
}
