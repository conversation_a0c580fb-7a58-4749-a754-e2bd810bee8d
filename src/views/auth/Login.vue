<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>后台管理系统</h2>
        <p>欢迎登录</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            :prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <!-- <el-form-item prop="role">
          <el-select
            v-model="loginForm.role"
            placeholder="请选择角色"
            size="large"
            style="width: 100%"
            @change="handleRoleChange"
          >
            <el-option
              v-for="role in roleOptions"
              :key="role.value"
              :label="role.label"
              :value="role.value"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ role.label }}</span>
                <el-tag :color="role.color" size="small" style="margin-left: 10px;">
                  {{ role.desc }}
                </el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item> -->
        
        <!-- <el-form-item>
          <el-checkbox v-model="loginForm.remember" label="记住密码" />
        </el-form-item> -->
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 测试账号提示 -->
      <!-- <div class="login-footer">
        <el-card class="test-accounts">
          <template #header>
            <span>测试账号</span>
          </template>
          <div class="account-list">
            <div 
              v-for="account in testAccounts" 
              :key="account.role"
              class="account-item"
              @click="quickLogin(account)"
            >
              <el-tag :color="account.color" size="small">{{ account.label }}</el-tag>
              <span>{{ account.username }} / {{ account.password }}</span>
              <el-button type="text" size="small">快速登录</el-button>
            </div>
          </div>
        </el-card>
      </div> -->
    </div>
  </div>
</template>

<script setup>
import { useLogin } from './Login.js'

// 使用登录组合式函数
const {
  // 图标组件
  User,
  Lock,
  // 表单引用
  loginFormRef,
  // 响应式数据
  loading,
  loginForm,
  loginRules,
  testAccounts,
  quickLoginOptions,
  roleOptions,
  // 方法
  handleRoleChange,
  quickLogin,
  handleLogin
} = useLogin()
</script>

<style scoped src="./Login.css"></style>
