<template>
  <div class="simple-demo-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">超简化组件使用示例</h1>
      <p class="page-description">只需配置JSON和回调函数即可使用</p>
    </div>

    <!-- 简化表格示例 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <h2>SimpleTable - 超简化表格</h2>
      </template>
      
      <div class="demo-section">
        <h3>用法示例</h3>
        <pre class="code-block">{{ tableUsageCode }}</pre>
        
        <h3>实际效果</h3>
        <SimpleTable 
          :data="tableData"
          :config="tableConfig"
          :loading="tableLoading"
          :total="100"
          :current-page="currentPage"
          :page-size="pageSize"
          @action="handleTableAction"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 简化表单示例 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <h2>SimpleForm - 超简化表单</h2>
      </template>
      
      <div class="demo-section">
        <h3>用法示例</h3>
        <pre class="code-block">{{ formUsageCode }}</pre>
        
        <h3>实际效果</h3>
        <SimpleForm 
          :config="formConfig"
          :initial-data="formInitialData"
          @submit="handleFormSubmit"
          @change="handleFormChange"
        />
      </div>
    </el-card>

    <!-- 配置对比 -->
    <el-card class="demo-card" shadow="never">
      <template #header>
        <h2>配置复杂度对比</h2>
      </template>
      
      <div class="demo-section">
        <div class="comparison">
          <div class="comparison-item">
            <h3>优化前（复杂配置）</h3>
            <pre class="code-block old">{{ oldConfigCode }}</pre>
          </div>
          <div class="comparison-item">
            <h3>优化后（超简化）</h3>
            <pre class="code-block new">{{ newConfigCode }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import SimpleTable from '@/components/table/SimpleTable.vue'
import SimpleForm from '@/components/form/SimpleForm.vue'

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)

// 表格配置
const tableConfig = {
  columns: ['id', 'username', 'email', 'status', 'createTime'],
  actions: [
    { type: 'edit', label: '编辑', style: 'primary' },
    { type: 'delete', label: '删除', style: 'danger' }
  ],
  pagination: true,
  selection: true
}

// 表格数据
const tableData = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    status: '1',
    createTime: '2024-06-19 10:00:00'
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    status: '0',
    createTime: '2024-06-18 15:30:00'
  }
])

const tableLoading = ref(false)

// 表单配置
const formConfig = {
  fields: ['username', 'email', 'password', 'status', 'description'],
  actions: [
    { type: 'submit', label: '提交', style: 'primary' },
    { type: 'reset', label: '重置', style: 'default' }
  ]
}

// 表单初始数据
const formInitialData = {
  username: '',
  email: '',
  password: '',
  status: '1',
  description: ''
}

// 表格操作处理
const handleTableAction = ({ type, row }) => {
  if (type === 'edit') {
    ElMessage.success(`编辑用户：${row.username}`)
  } else if (type === 'delete') {
    ElMessage.warning(`删除用户：${row.username}`)
  }
}

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
  ElMessage.info(`切换到第 ${page} 页`)
}

// 页面大小变化处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  ElMessage.info(`每页显示 ${size} 条`)
}

// 表单提交处理
const handleFormSubmit = (data) => {
  ElMessage.success('表单提交成功')
  console.log('表单数据:', data)
}

// 表单变化处理
const handleFormChange = (data) => {
  console.log('表单数据变化:', data)
}

// 使用示例代码
const tableUsageCode = `// 表格使用 - 只需配置JSON和回调
<SimpleTable 
  :data="tableData"
  :config="{
    columns: ['id', 'username', 'email', 'status', 'createTime'],
    actions: [
      { type: 'edit', label: '编辑', style: 'primary' },
      { type: 'delete', label: '删除', style: 'danger' }
    ],
    pagination: true,
    selection: true
  }"
  :total="100"
  :current-page="currentPage"
  :page-size="pageSize"
  @action="handleTableAction"
  @page-change="handlePageChange"
  @size-change="handleSizeChange"
/>`

const formUsageCode = `// 表单使用 - 只需配置JSON和回调
<SimpleForm 
  :config="{
    fields: ['username', 'email', 'password', 'status', 'description'],
    actions: [
      { type: 'submit', label: '提交', style: 'primary' },
      { type: 'reset', label: '重置', style: 'default' }
    ]
  }"
  :initial-data="formData"
  @submit="handleFormSubmit"
  @change="handleFormChange"
/>`

const oldConfigCode = `// 优化前：需要配置大量细节
const tableConfig = {
  columns: [
    {
      prop: 'id',
      label: 'ID',
      width: 80
    },
    {
      prop: 'username',
      label: '用户名',
      width: 120
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      type: 'tag',
      tagMap: {
        '1': { type: 'success', text: '启用' },
        '0': { type: 'danger', text: '禁用' }
      }
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160,
      type: 'datetime'
    }
  ],
  actions: [
    {
      type: 'edit',
      label: '编辑',
      style: 'primary',
      icon: 'Edit',
      permission: 'user:edit'
    }
  ],
  pagination: {
    show: true,
    currentPage: 1,
    pageSize: 10,
    total: 0,
    pageSizes: [10, 20, 50, 100]
  },
  selection: true,
  border: true,
  stripe: true
}`

const newConfigCode = `// 优化后：智能推断，极简配置
const tableConfig = {
  columns: ['id', 'username', 'status', 'createTime'],
  actions: [
    { type: 'edit', label: '编辑', style: 'primary' }
  ],
  pagination: true,
  selection: true
}

// 自动推断：
// - id -> 文本列，80px宽度
// - username -> 文本列，自动标题"用户名"
// - status -> 标签列，自动标签映射
// - createTime -> 日期时间列，160px宽度`
</script>

<style scoped>
.simple-demo-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.demo-card {
  margin-bottom: 30px;
}

.demo-card h2 {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.demo-section h3 {
  font-size: 16px;
  font-weight: 500;
  color: #409eff;
  margin: 20px 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.code-block {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #606266;
  overflow-x: auto;
  white-space: pre-wrap;
}

.comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.comparison-item h3 {
  margin-top: 0;
}

.code-block.old {
  background: #fef0f0;
  border-color: #fbc4c4;
  color: #f56c6c;
}

.code-block.new {
  background: #f0f9ff;
  border-color: #b3d8ff;
  color: #409eff;
}

@media (max-width: 768px) {
  .comparison {
    grid-template-columns: 1fr;
  }
  
  .simple-demo-page {
    padding: 15px;
  }
}
</style>
