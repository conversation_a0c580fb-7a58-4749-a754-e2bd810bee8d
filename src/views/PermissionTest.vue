<template>
  <div class="permission-test-page">
    <el-card>
      <template #header>
        <h2>权限测试页面</h2>
        <p>测试权限拒绝时只弹窗不重定向</p>
      </template>

      <div class="test-section">
        <h3>当前用户信息</h3>
        <el-descriptions border>
          <el-descriptions-item label="用户名">{{ currentUser?.username || '未登录' }}</el-descriptions-item>
          <el-descriptions-item label="角色">{{ currentUser?.role || '无' }}</el-descriptions-item>
          <el-descriptions-item label="角色名称">{{ roleConfig?.name || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="test-section">
        <h3>当前用户权限</h3>
        <div class="permissions-grid">
          <el-tag 
            v-for="permission in userPermissions" 
            :key="permission"
            type="success"
            size="small"
            style="margin: 4px;"
          >
            {{ getPermissionName(permission) }}
          </el-tag>
        </div>
      </div>

      <div class="test-section">
        <h3>权限测试按钮</h3>
        <div class="test-buttons">
          <el-button 
            type="primary" 
            @click="testPermission('user:create')"
          >
            测试用户创建权限
          </el-button>
          
          <el-button 
            type="danger" 
            @click="testPermission('user:delete')"
          >
            测试用户删除权限
          </el-button>
          
          <el-button 
            type="warning" 
            @click="testPermission('system:config')"
          >
            测试系统配置权限
          </el-button>
          
          <el-button 
            type="info" 
            @click="testPermission('system:backup')"
          >
            测试系统备份权限
          </el-button>

          <el-button 
            type="success" 
            @click="testAnyPermissions(['role:create', 'role:edit'])"
          >
            测试角色管理权限（任一）
          </el-button>
        </div>
      </div>

      <div class="test-section">
        <h3>组合式函数测试</h3>
        <div class="test-buttons">
          <el-button 
            type="primary" 
            @click="testComposablePermission('content:edit')"
          >
            测试 checkPermission
          </el-button>
          
          <el-button 
            type="warning" 
            @click="testComposableAnyPermissions(['file:delete', 'file:upload'])"
          >
            测试 checkAnyPermission
          </el-button>
        </div>
      </div>

      <div class="test-section">
        <h3>指令权限测试</h3>
        <div class="test-buttons">
          <!-- 这些按钮会根据权限显示/隐藏 -->
          <el-button 
            v-permission="'user:create'"
            type="primary"
          >
            有权限时显示（用户创建）
          </el-button>
          
          <el-button 
            v-permission="'system:config'"
            type="danger"
          >
            有权限时显示（系统配置）
          </el-button>
          
          <el-button 
            v-permission="['role:create', 'role:edit']"
            type="warning"
          >
            有权限时显示（角色管理任一）
          </el-button>
        </div>
      </div>

      <div class="test-section">
        <h3>测试说明</h3>
        <el-alert
          title="测试说明"
          type="info"
          description="点击上方按钮测试权限检查功能。如果没有权限，应该弹出提示框而不是重定向到其他页面。"
          show-icon
          :closable="false"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { usePermissionTest } from './PermissionTest.js'

// 使用权限测试组合式函数
const {
  currentUser,
  userPermissions,
  roleConfig,
  getPermissionName,
  testPermission,
  testAnyPermissions,
  testComposablePermission,
  testComposableAnyPermissions
} = usePermissionTest()
</script>

<style scoped src="./PermissionTest.css"></style>
