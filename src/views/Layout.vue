<template>
  <div class="layout-container">
    <el-container>
      <!-- 左侧菜单 -->
      <el-aside 
        :width="isCollapse ? '64px' : '200px'" 
        class="sidebar"
      >
        <LeftMenu :collapse="isCollapse" />
      </el-aside>
      
      <!-- 主体区域 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <TopNavigation 
            :collapse="isCollapse" 
            @toggle-sidebar="toggleSidebar"
          />
        </el-header>
        
        <!-- 主内容区 -->
        <el-main class="main-content">
          <MainContent />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import LeftMenu from '@/components/layout/Left.vue'
import TopNavigation from '@/components/layout/Top.vue'
import MainContent from '@/components/layout/Main.vue'

// 侧边栏折叠状态
const isCollapse = ref(false)

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.layout-container :deep(.el-container) {
  height: 100%;
}

.layout-container :deep(.el-container--vertical) {
  height: 100%;
}

.sidebar {
  background-color: #001529;
  transition: width 0.3s;
  border-right: 1px solid #e6e6e6;
  height: 100vh;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0;
  height: 60px !important;
  line-height: 60px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  flex-shrink: 0;
}

.main-content {
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 60px);
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    z-index: 1000;
    height: 100vh;
  }
  
  .main-content {
    margin-left: 0;
    padding: 15px;
  }
}
</style>
