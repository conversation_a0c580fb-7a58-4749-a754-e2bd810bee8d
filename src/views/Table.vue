<template>
  <div class="table-page">
    <el-card class="table-container">
      <template #header>
        <div class="table-header">
          <h3>{{ currentConfig.title || '数据表格' }}</h3>
          <el-button-group>
            <el-button @click="switchConfig('users')">用户列表</el-button>
            <el-button @click="switchConfig('orders')">订单管理</el-button>
            <el-button @click="switchConfig('products')">商品管理</el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 搜索和操作栏 -->
      <div class="table-toolbar">
        <div class="search-bar">
          <el-input
            v-model="searchText"
            placeholder="搜索..."
            clearable
            style="width: 300px"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="action-buttons">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>

      <!-- 配置化表格 -->
      <ConfigurableTable
        ref="tableRef"
        :config="currentConfig"
        :data="tableData"
        :loading="loading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @row-click="handleRowClick"
        @edit="handleEdit"
        @delete="handleDelete"
      />

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 示例配置展示 -->
    <el-card class="config-display" style="margin-top: 20px;">
      <template #header>
        <h4>当前表格配置 (JSON)</h4>
      </template>
      <el-input
        v-model="configJson"
        type="textarea"
        :rows="8"
        readonly
        placeholder="表格配置 JSON"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Refresh, Download } from '@element-plus/icons-vue'
import ConfigurableTable from '@/components/table/ConfigurableTable.vue'
import { request } from '@/api'

// 表格引用
const tableRef = ref()

// 当前表格配置类型
const currentConfigType = ref('users')

// 搜索文本
const searchText = ref('')

// 表格数据
const tableData = ref([])

// 加载状态
const loading = ref(false)

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 用户列表配置
const usersTableConfig = {
  title: '用户管理',
  selectable: true,
  stripe: true,
  border: true,
  columns: [
    {
      prop: 'id',
      label: 'ID',
      width: 80,
      sortable: true
    },
    {
      prop: 'name',
      label: '姓名',
      minWidth: 120,
      searchable: true
    },
    {
      prop: 'email',
      label: '邮箱',
      minWidth: 180,
      searchable: true
    },
    {
      prop: 'phone',
      label: '手机号',
      width: 140
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      type: 'tag',
      tagMap: {
        active: { type: 'success', text: '活跃' },
        inactive: { type: 'danger', text: '禁用' },
        pending: { type: 'warning', text: '待审核' }
      }
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160,
      type: 'datetime',
      sortable: true
    },
    {
      prop: 'actions',
      label: '操作',
      width: 150,
      type: 'actions',
      actions: [
        { type: 'edit', label: '编辑', style: 'primary' },
        { type: 'delete', label: '删除', style: 'danger' }
      ]
    }
  ]
}

// 订单管理配置
const ordersTableConfig = {
  title: '订单管理',
  stripe: true,
  border: true,
  columns: [
    {
      prop: 'orderNo',
      label: '订单号',
      width: 160,
      searchable: true
    },
    {
      prop: 'customerName',
      label: '客户姓名',
      width: 120
    },
    {
      prop: 'amount',
      label: '订单金额',
      width: 120,
      type: 'currency',
      sortable: true
    },
    {
      prop: 'status',
      label: '订单状态',
      width: 120,
      type: 'tag',
      tagMap: {
        pending: { type: 'warning', text: '待支付' },
        paid: { type: 'success', text: '已支付' },
        shipped: { type: 'primary', text: '已发货' },
        completed: { type: 'success', text: '已完成' },
        cancelled: { type: 'danger', text: '已取消' }
      }
    },
    {
      prop: 'orderTime',
      label: '下单时间',
      width: 160,
      type: 'datetime',
      sortable: true
    },
    {
      prop: 'actions',
      label: '操作',
      width: 180,
      type: 'actions',
      actions: [
        { type: 'edit', label: '编辑', style: 'primary' },
        { type: 'delete', label: '删除', style: 'danger' }
      ]
    }
  ]
}

// 商品管理配置
const productsTableConfig = {
  title: '商品管理',
  stripe: true,
  border: true,
  columns: [
    {
      prop: 'image',
      label: '商品图片',
      width: 100,
      type: 'image'
    },
    {
      prop: 'name',
      label: '商品名称',
      minWidth: 180,
      searchable: true
    },
    {
      prop: 'category',
      label: '分类',
      width: 120
    },
    {
      prop: 'price',
      label: '价格',
      width: 100,
      type: 'currency',
      sortable: true
    },
    {
      prop: 'stock',
      label: '库存',
      width: 80,
      sortable: true
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      type: 'tag',
      tagMap: {
        on_sale: { type: 'success', text: '在售' },
        off_sale: { type: 'danger', text: '下架' },
        out_of_stock: { type: 'warning', text: '缺货' }
      }
    },
    {
      prop: 'actions',
      label: '操作',
      width: 150,
      type: 'actions',
      actions: [
        { type: 'edit', label: '编辑', style: 'primary' },
        { type: 'delete', label: '删除', style: 'danger' }
      ]
    }
  ]
}

// 表格配置映射
const tableConfigs = {
  users: usersTableConfig,
  orders: ordersTableConfig,
  products: productsTableConfig
}

// 当前表格配置
const currentConfig = computed(() => tableConfigs[currentConfigType.value])

// 配置 JSON 字符串
const configJson = computed(() => 
  JSON.stringify(currentConfig.value, null, 2)
)

// 切换表格配置
const switchConfig = (type) => {
  currentConfigType.value = type
  loadTableData()
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    // 使用新的API系统
    // const response = await request.get('/table/data', {
    //   type: currentConfigType.value,
    //   page: pagination.currentPage,
    //   pageSize: pagination.pageSize,
    //   search: searchText.value
    // })
    
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 生成模拟数据
    const mockData = generateMockData(currentConfigType.value)
    tableData.value = mockData.data
    pagination.total = mockData.total
    
    ElMessage.success('数据加载成功')
  } catch (error) {
    console.error('数据加载失败:', error)
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockData = (type) => {
  const pageSize = pagination.pageSize
  const currentPage = pagination.currentPage
  
  if (type === 'users') {
    const data = Array.from({ length: pageSize }, (_, index) => ({
      id: (currentPage - 1) * pageSize + index + 1,
      name: `用户${(currentPage - 1) * pageSize + index + 1}`,
      email: `user${index + 1}@example.com`,
      phone: `138${String(index).padStart(8, '0')}`,
      status: ['active', 'inactive', 'pending'][index % 3],
      createTime: new Date(Date.now() - Math.random() * 10000000000).toISOString()
    }))
    return { data, total: 95 }
  } else if (type === 'orders') {
    const data = Array.from({ length: pageSize }, (_, index) => ({
      orderNo: `ORD${Date.now()}${index}`,
      customerName: `客户${index + 1}`,
      amount: (Math.random() * 1000 + 100).toFixed(2),
      status: ['pending', 'paid', 'shipped', 'completed', 'cancelled'][index % 5],
      orderTime: new Date(Date.now() - Math.random() * 10000000000).toISOString()
    }))
    return { data, total: 128 }
  } else if (type === 'products') {
    const data = Array.from({ length: pageSize }, (_, index) => ({
      image: `https://via.placeholder.com/80x80?text=P${index + 1}`,
      name: `商品${index + 1}`,
      category: ['电子产品', '服装', '食品', '图书'][index % 4],
      price: (Math.random() * 500 + 50).toFixed(2),
      stock: Math.floor(Math.random() * 100),
      status: ['on_sale', 'off_sale', 'out_of_stock'][index % 3]
    }))
    return { data, total: 76 }
  }
  return { data: [], total: 0 }
}

// 处理搜索
const handleSearch = () => {
  console.log('搜索:', searchText.value)
  loadTableData()
}

// 处理新增
const handleAdd = () => {
  ElMessage.info('新增功能开发中...')
}

// 处理刷新
const handleRefresh = () => {
  loadTableData()
}

// 处理导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

// 处理排序变化
const handleSortChange = (sort) => {
  console.log('排序变化:', sort)
  loadTableData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击:', row)
}

// 处理编辑
const handleEdit = (row) => {
  console.log('编辑:', row)
  ElMessage.info('编辑功能开发中...')
}

// 处理删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟删除操作
    ElMessage.success('删除成功')
    loadTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadTableData()
}

// 处理当前页变化
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadTableData()
}

// 页面挂载时加载数据
onMounted(() => {
  loadTableData()
})
</script>

<style scoped>
.table-page {
  padding: 20px;
}

.table-container {
  max-width: 1200px;
  margin: 0 auto;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  margin: 0;
  color: #303133;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.config-display {
  max-width: 1200px;
  margin: 0 auto;
}

:deep(.el-textarea__inner) {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-page {
    padding: 10px;
  }
  
  .table-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .table-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .search-bar .el-input {
    width: 100% !important;
  }
}
</style>
