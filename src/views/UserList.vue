<template>
  <div class="user-list-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">用户列表</h1>
      <div class="header-actions">
        <el-button 
          v-permission="'user:create'"
          type="primary" 
          @click="handleAdd"
        >
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input 
            v-model="searchForm.username" 
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input 
            v-model="searchForm.email" 
            placeholder="请输入邮箱"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <ConfigurableTable 
        :config="tableConfig"
        :data="tableData"
        :loading="loading"
        @edit="handleEdit"
        @delete="handleDelete"
        @view="handleView"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </el-card>

  </div>
</template>

<script setup>
import { useUserList } from './UserList.js'
import ConfigurableTable from '@/components/table/ConfigurableTable.vue'

// 使用用户列表组合式函数
const {
  // 图标组件
  Plus,
  Search,
  Refresh,
  // 响应式数据
  searchForm,
  tableData,
  loading,
  tableConfig,
  // 方法
  handleSearch,
  handleReset,
  handleAdd,
  handleView,
  handleEdit,
  handleDelete,
  handlePageChange,
  handleSizeChange
} = useUserList()
</script>

<style scoped src="./UserList.css"></style>
