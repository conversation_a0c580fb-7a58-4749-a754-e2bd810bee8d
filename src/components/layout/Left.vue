<template>
  <div class="left-menu">
    <!-- Logo 区域 -->
    <div class="logo-container">
      <div class="logo">
        <el-icon v-if="!collapse" class="logo-icon">
          <Management />
        </el-icon>
        <span v-if="!collapse" class="logo-text">管理系统</span>
        <el-icon v-if="collapse" class="logo-icon-collapse">
          <Management />
        </el-icon>
      </div>
    </div>

    <!-- 菜单区域 -->
    <el-menu
      :default-active="currentRoute"
      class="sidebar-menu"
      background-color="#001529"
      text-color="#fff"
      active-text-color="#1890ff"
      :collapse="collapse"
      :collapse-transition="false"
      router
    >
      <!-- 菜单加载状态 -->
      <div v-if="menuLoading" class="menu-loading">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span v-if="!collapse">加载中...</span>
      </div>

      <!-- 菜单列表 -->
      <template v-else>
        <template v-for="menu in menuList" :key="menu.path">
          <!-- 使用v-memo优化渲染性能 -->
          <div v-memo="[menu.path, menu.title, menu.children?.length, collapse]">
            <!-- 有子菜单的项 -->
            <el-sub-menu v-if="menu.children && menu.children.length > 0" :index="menu.path">
              <template #title>
                <el-icon>
                  <component :is="menu.icon" />
                </el-icon>
                <span>{{ menu.title }}</span>
              </template>

              <!-- 二级菜单项 -->
              <template v-for="child in menu.children" :key="child.path">
                <!-- 如果有三级菜单 -->
                <el-sub-menu
                  v-if="child.children && child.children.length > 0"
                  :index="child.path"
                >
                  <template #title>
                    <el-icon>
                      <component :is="child.icon" />
                    </el-icon>
                    <span>{{ child.title }}</span>
                  </template>

                  <!-- 三级菜单项 -->
                  <el-menu-item
                    v-for="grandChild in child.children"
                    :key="grandChild.path"
                    :index="grandChild.path"
                    @click="handleMenuClick(grandChild)"
                  >
                    <el-icon>
                      <component :is="grandChild.icon" />
                    </el-icon>
                    <template #title>
                      <span>{{ grandChild.title }}</span>
                    </template>
                  </el-menu-item>
                </el-sub-menu>

                <!-- 普通二级菜单项 -->
                <el-menu-item
                  v-else
                  :index="child.path"
                  @click="handleMenuClick(child)"
                >
                  <el-icon>
                    <component :is="child.icon" />
                  </el-icon>
                  <template #title>
                    <span>{{ child.title }}</span>
                  </template>
                </el-menu-item>
              </template>
            </el-sub-menu>

            <!-- 无子菜单的项 -->
            <el-menu-item
              v-else
              :index="menu.path"
              @click="handleMenuClick(menu)"
            >
              <el-icon>
                <component :is="menu.icon" />
              </el-icon>
              <template #title>
                <span>{{ menu.title }}</span>
              </template>
            </el-menu-item>
          </div>
        </template>
      </template>
    </el-menu>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, markRaw, shallowRef, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import {
  Management,
  Edit,
  Grid,
  User,
  UserFilled,
  EditPen,
  Monitor,
  Operation,
  MagicStick,
  Avatar,
  Plus,
  List,
  ShoppingCart,
  Goods,
  ShoppingBag,
  Setting,
  Tools,
  Lock,
  Sunny,
  Document,
  DocumentChecked,
  Guide,
  Notebook,
  TrendCharts,
  DataLine,
  DataBoard,
  PieChart,
  Loading
} from '@element-plus/icons-vue'
import { filterMenuByPermission } from '@/utils/permission'
import { generateMenus } from '@/router/modules/index.js'

const props = defineProps({
  collapse: {
    type: Boolean,
    default: false
  }
})

const route = useRoute()

// 当前路由 - 用于菜单高亮
const currentRoute = computed(() => {
  const currentPath = route.path

  // 递归查找匹配的菜单项路径
  const findMatchingMenuPath = (menus, targetPath) => {
    for (const menu of menus) {
      // 直接匹配菜单项
      if (menu.path === targetPath) {
        return menu.path
      }

      // 检查是否是动态路由匹配
      if (menu.path && targetPath.startsWith(menu.path.split('/:')[0])) {
        return menu.path
      }

      // 递归查找子菜单
      if (menu.children && menu.children.length > 0) {
        const childMatch = findMatchingMenuPath(menu.children, targetPath)
        if (childMatch) {
          return childMatch
        }
      }
    }
    return null
  }

  const matchedPath = findMatchingMenuPath(menuList.value, currentPath)
  const result = matchedPath || currentPath

  console.log('当前路径:', currentPath, '匹配菜单:', result)
  return result
})

// 菜单列表 - 使用shallowRef提升性能
const menuList = shallowRef([])
// 菜单加载状态
const menuLoading = ref(false)

// 处理菜单点击
const handleMenuClick = (menu) => {
  console.log('菜单点击:', menu)
  // 路由跳转由 el-menu 的 router 属性自动处理
}

// 菜单缓存
let menuCache = null
let lastCacheTime = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

// 初始化菜单 - 优化版本
const initMenu = async () => {
  if (menuLoading.value) return // 防止重复加载

  // 检查缓存
  const now = Date.now()
  if (menuCache && (now - lastCacheTime) < CACHE_DURATION) {
    menuList.value = menuCache
    return
  }

  menuLoading.value = true

  try {
    // 使用 nextTick 确保在下一个事件循环中执行，避免阻塞UI
    await nextTick()

    // 根据配置生成层级菜单
    const hierarchicalMenus = generateMenus(false) // 层级菜单

    // 根据权限过滤菜单
    const filteredHierarchicalMenus = filterMenuByPermission(hierarchicalMenus)

    // 递归处理菜单图标（支持3级菜单）- 优化版本
    const processMenuIcons = (menuItems) => {
      return menuItems.map(item => {
        const processedItem = {
          ...item,
          icon: getIconComponent(item.icon)
        }

        // 只有当children存在且不为空时才递归处理
        if (item.children && item.children.length > 0) {
          processedItem.children = processMenuIcons(item.children)
        }

        return processedItem
      })
    }

    const processedMenus = processMenuIcons(filteredHierarchicalMenus)

    // 更新缓存
    menuCache = processedMenus
    lastCacheTime = now

    // 使用shallowRef的特性，直接赋值触发更新
    menuList.value = processedMenus

  } catch (error) {
    console.error('菜单初始化失败:', error)
  } finally {
    menuLoading.value = false
  }
}

// 图标组件映射 - 使用Map提升查找性能
const iconMap = new Map([
  ['Edit', markRaw(Edit)],
  ['Grid', markRaw(Grid)],
  ['Management', markRaw(Management)],
  ['User', markRaw(User)],
  ['UserFilled', markRaw(UserFilled)],
  ['EditPen', markRaw(EditPen)],
  ['Monitor', markRaw(Monitor)],
  ['Operation', markRaw(Operation)],
  ['MagicStick', markRaw(MagicStick)],
  ['Avatar', markRaw(Avatar)],
  ['Plus', markRaw(Plus)],
  ['List', markRaw(List)],
  ['ShoppingCart', markRaw(ShoppingCart)],
  ['Goods', markRaw(Goods)],
  ['ShoppingBag', markRaw(ShoppingBag)],
  ['Setting', markRaw(Setting)],
  ['Tools', markRaw(Tools)],
  ['Lock', markRaw(Lock)],
  ['Sunny', markRaw(Sunny)],
  ['Document', markRaw(Document)],
  ['DocumentChecked', markRaw(DocumentChecked)],
  ['Guide', markRaw(Guide)],
  ['Notebook', markRaw(Notebook)],
  ['TrendCharts', markRaw(TrendCharts)],
  ['DataLine', markRaw(DataLine)],
  ['DataBoard', markRaw(DataBoard)],
  ['PieChart', markRaw(PieChart)]
])

// 获取图标组件 - 优化版本
const getIconComponent = (iconName) => {
  return iconMap.get(iconName) || markRaw(Edit)
}

// 页面挂载时初始化
onMounted(() => {
  initMenu()
})
</script>

<style scoped>
.left-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logo-container {
  padding: 16px;
  background-color: #002140;
  border-bottom: 1px solid #1f2f3d;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  overflow: hidden;
}

.logo-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 8px;
}

.logo-icon-collapse {
  font-size: 24px;
  color: #1890ff;
}

.logo-text {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  white-space: nowrap;
}

.sidebar-menu {
  flex: 1;
  border-right: none;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 200px;
}



/* 菜单项样式 */
:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #1f2f3d;
  color: #fff !important;
}

:deep(.el-menu-item:hover) {
  background-color: #263445 !important;
  color: #fff !important;
}

:deep(.el-menu-item.is-active) {
  background-color: #1890ff !important;
  color: #fff !important;
  border-right: 3px solid #1890ff;
}

:deep(.el-menu-item .el-icon) {
  color: inherit;
}

:deep(.el-menu-item span) {
  color: inherit;
}

/* 菜单加载状态样式 */
.menu-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #8c8c8c;
  font-size: 14px;
}

.menu-loading .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.menu-loading span {
  color: #8c8c8c;
}



/* 折叠状态下的样式 */
:deep(.el-menu--collapse .el-menu-item) {
  padding: 0 !important;
  text-align: center;
}

:deep(.el-menu--collapse .el-menu-item .el-icon) {
  margin-right: 0;
  vertical-align: middle;
  width: 24px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .left-menu {
    position: fixed;
    left: 0;
    top: 0;
    width: 200px;
    z-index: 1000;
    box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  }
}

/* 过渡动画 */
.logo-container,
.sidebar-menu {
  transition: all 0.3s;
}
</style>
