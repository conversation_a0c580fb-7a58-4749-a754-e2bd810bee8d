import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import { hasPermission, hasAnyPermission } from '@/utils/permission'
import { generateRoutes, generateMenus } from './modules/index.js'

// 从模块配置生成路由
const routes = generateRoutes()

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 路由跳转时滚动到顶部
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 后台管理系统`
  }

  // 检查是否需要认证
  if (to.meta.requireAuth) {
    const token = localStorage.getItem('token')
    if (token) {
      // 已登录，检查权限
      if (to.meta.permission) {
        let hasAccess = false
        
        if (typeof to.meta.permission === 'string') {
          hasAccess = hasPermission(to.meta.permission)
        } else if (Array.isArray(to.meta.permission)) {
          hasAccess = hasAnyPermission(to.meta.permission)
        }
        
        if (hasAccess) {
          next()
        } else {
          ElMessage.error('权限不足，无法访问该页面')
          next('/admin/dashboard') // 重定向到首页
        }
      } else {
        // 没有权限要求，直接允许访问
        next()
      }
    } else {
      // 未登录，跳转到登录页
      ElMessage.warning('请先登录')
      next({
        path: '/login',
        query: { redirect: to.fullPath } // 保存原路径，登录后跳转
      })
    }
  } else {
    // 不需要认证的路由直接访问
    if (to.path === '/login') {
      const token = localStorage.getItem('token')
      if (token) {
        // 已登录用户访问登录页，重定向到管理后台
        next('/admin')
        return
      }
    }
    next()
  }
})

// 全局后置守卫
router.afterEach((to, from) => {
  // 路由跳转完成后的处理
  console.log(`路由跳转: ${from.path} -> ${to.path}`)
})

// 从模块配置生成菜单配置，供左侧菜单组件使用
export const menuConfig = generateMenus()

// 工具函数：检查用户是否已登录
export function isAuthenticated() {
  // 检查 localStorage 中的 token
  const token = localStorage.getItem('token')
  if (token) {
    return true
  }

  // 检查是否有认证相关的 cookie（由于 withCredentials: true，cookie 会自动发送）
  // 这里我们可以检查是否有用户信息，如果有说明可能通过 cookie 认证
  const userInfo = localStorage.getItem('userInfo')
  if (userInfo) {
    return true
  }

  // 如果都没有，则认为未登录
  return false
}

// 工具函数：获取用户信息
export function getUserInfo() {
  const userInfo = localStorage.getItem('userInfo')
  return userInfo ? JSON.parse(userInfo) : null
}

// 工具函数：登出
export async function logout() {
  try {
    // 尝试调用后端登出接口
    const { logout: apiLogout } = await import('@/api/modules/auth')
    await apiLogout()
  } catch (error) {
    console.warn('登出API调用失败:', error)
  } finally {
    // 无论API是否成功，都清除本地数据
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')

    // 清除可能存在的认证相关cookie
    // 注意：前端无法直接清除HttpOnly cookie，需要后端配合
    document.cookie.split(';').forEach(cookie => {
      const [name] = cookie.trim().split('=')
      if (name && (name.includes('auth') || name.includes('token') || name.includes('session'))) {
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
      }
    })

    router.push('/login')
    ElMessage.success('已退出登录')
  }
}

export default router
